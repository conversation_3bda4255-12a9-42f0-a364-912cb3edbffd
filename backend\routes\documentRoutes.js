const express = require('express');
const router = express.Router();
const Document = require('../models/Document');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, owner, category, status } = req.query;
    const query = {};
    if (owner) query.owner = owner;
    if (category) query.category = category;
    if (status) query.status = status;
    
    const documents = await Document.find(query)
      .populate('owner', 'firstName lastName email')
      .populate('uploadedBy', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Document.countDocuments(query);
    res.json({ documents, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const document = await Document.findById(req.params.id)
      .populate('owner', 'firstName lastName email')
      .populate('uploadedBy', 'firstName lastName');
    
    if (!document) return res.status(404).json({ message: 'Document not found' });
    res.json(document);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const document = new Document(req.body);
    await document.save();
    res.status(201).json(document);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const document = await Document.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!document) return res.status(404).json({ message: 'Document not found' });
    res.json(document);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/approve', async (req, res) => {
  try {
    const { approvedBy, comments } = req.body;
    const document = await Document.findById(req.params.id);
    
    if (!document) return res.status(404).json({ message: 'Document not found' });
    
    await document.approve(approvedBy, comments);
    res.json({ message: 'Document approved successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/search', async (req, res) => {
  try {
    const { q, userId, category, status, dateFrom, dateTo } = req.query;
    
    const filters = {};
    if (category) filters.category = category;
    if (status) filters.status = status;
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;
    
    const documents = await Document.search(q, userId, filters);
    res.json(documents);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
