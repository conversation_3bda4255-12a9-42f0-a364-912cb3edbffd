const mongoose = require('mongoose');

const moduleSchema = new mongoose.Schema({
  // Informations de base du module
  moduleCode: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^MOD\d{3}$/, 'Format: MOD123']
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  
  // Détails académiques
  creditHours: {
    type: Number,
    required: true,
    min: 1,
    max: 20
  },
  duration: {
    weeks: { type: Number, required: true, min: 1 },
    intensiveFormat: { type: Boolean, default: false },
    totalContactHours: { type: Number, required: true, min: 1 }
  },
  
  // Classification du module
  category: {
    type: String,
    enum: ['Core', 'Elective', 'Specialization', 'Foundation', 'Capstone'],
    required: true
  },
  level: {
    type: String,
    enum: ['Beginner', 'Intermediate', 'Advanced', 'Expert'],
    required: true
  },
  theme: {
    type: String,
    enum: ['Leadership', 'Strategy', 'Finance', 'Marketing', 'Operations', 'Innovation', 'Digital Transformation', 'Sustainability', 'Global Business'],
    required: true
  },
  
  // Cours composant le module
  courses: [{
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course',
      required: true
    },
    sequence: { type: Number, required: true, min: 1 },
    isRequired: { type: Boolean, default: true },
    weight: { type: Number, min: 0, max: 100, default: 100 }
  }],
  
  // Prérequis
  prerequisites: [{
    module: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Module'
    },
    minimumGrade: { type: Number, min: 0, max: 20, default: 10 },
    description: { type: String, trim: true }
  }],
  
  // Coordinateur et équipe pédagogique
  coordinator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor',
    required: true
  },
  instructors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor'
  }],
  
  // Planification
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Fall', 'Spring', 'Summer', 'Year-long'],
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  
  // Objectifs d'apprentissage du module
  learningOutcomes: [{
    outcome: { type: String, required: true, trim: true },
    competencyLevel: {
      type: String,
      enum: ['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation']
    },
    assessmentMethod: { type: String, trim: true }
  }],
  
  // Compétences développées
  competencies: [{
    name: { type: String, required: true, trim: true },
    category: {
      type: String,
      enum: ['Technical', 'Leadership', 'Communication', 'Analytical', 'Strategic', 'Interpersonal']
    },
    level: {
      type: String,
      enum: ['Developing', 'Proficient', 'Advanced', 'Expert']
    },
    description: { type: String, trim: true }
  }],
  
  // Méthodes pédagogiques
  teachingMethods: [{
    method: {
      type: String,
      enum: ['Lecture', 'Case Study', 'Workshop', 'Simulation', 'Project-based Learning', 'Peer Learning', 'Online Learning', 'Field Trip', 'Guest Speaker'],
      required: true
    },
    percentage: { type: Number, min: 0, max: 100 },
    description: { type: String, trim: true }
  }],
  
  // Évaluation du module
  assessment: {
    methods: [{
      type: {
        type: String,
        enum: ['Continuous Assessment', 'Final Exam', 'Project', 'Presentation', 'Portfolio', 'Peer Assessment', 'Self Assessment'],
        required: true
      },
      weight: { type: Number, required: true, min: 0, max: 100 },
      description: { type: String, trim: true },
      dueDate: { type: Date }
    }],
    passingGrade: { type: Number, default: 60, min: 0, max: 100 },
    gradingCriteria: [{ type: String, trim: true }]
  },
  
  // Ressources et matériaux
  resources: {
    requiredTexts: [{
      title: { type: String, required: true, trim: true },
      author: { type: String, required: true, trim: true },
      edition: { type: String, trim: true },
      isbn: { type: String, trim: true },
      publisher: { type: String, trim: true }
    }],
    recommendedReadings: [{
      title: { type: String, required: true, trim: true },
      author: { type: String, trim: true },
      source: { type: String, trim: true },
      url: { type: String, trim: true }
    }],
    onlineResources: [{
      title: { type: String, required: true, trim: true },
      url: { type: String, required: true, trim: true },
      type: {
        type: String,
        enum: ['Video', 'Article', 'Database', 'Software', 'Platform']
      }
    }],
    equipment: [{ type: String, trim: true }]
  },
  
  // Capacité et inscriptions
  capacity: {
    maximum: { type: Number, required: true, min: 1 },
    minimum: { type: Number, required: true, min: 1 },
    current: { type: Number, default: 0, min: 0 }
  },
  
  // Statut du module
  status: {
    type: String,
    enum: ['draft', 'approved', 'active', 'completed', 'cancelled', 'archived'],
    default: 'draft'
  },
  
  // Projet intégré ou travail final
  capstoneProject: {
    isRequired: { type: Boolean, default: false },
    title: { type: String, trim: true },
    description: { type: String, trim: true },
    deliverables: [{ type: String, trim: true }],
    timeline: [{
      milestone: { type: String, required: true, trim: true },
      dueDate: { type: Date, required: true },
      description: { type: String, trim: true }
    }],
    assessmentCriteria: [{ type: String, trim: true }]
  },
  
  // Partenariats et collaborations
  partnerships: [{
    organization: { type: String, required: true, trim: true },
    type: {
      type: String,
      enum: ['Corporate Partner', 'Academic Institution', 'Government Agency', 'NGO', 'Consulting Firm']
    },
    role: { type: String, trim: true },
    contactPerson: {
      name: { type: String, trim: true },
      email: { type: String, trim: true },
      phone: { type: String, trim: true }
    },
    contribution: { type: String, trim: true }
  }],
  
  // Feedback et évaluations
  feedback: {
    studentEvaluations: [{
      student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Student'
      },
      ratings: {
        contentRelevance: { type: Number, min: 1, max: 5 },
        instructorQuality: { type: Number, min: 1, max: 5 },
        learningExperience: { type: Number, min: 1, max: 5 },
        practicalApplication: { type: Number, min: 1, max: 5 },
        overallSatisfaction: { type: Number, min: 1, max: 5 }
      },
      comments: { type: String, trim: true },
      suggestions: { type: String, trim: true },
      submittedAt: { type: Date, default: Date.now }
    }],
    averageRatings: {
      contentRelevance: { type: Number, min: 1, max: 5, default: 0 },
      instructorQuality: { type: Number, min: 1, max: 5, default: 0 },
      learningExperience: { type: Number, min: 1, max: 5, default: 0 },
      practicalApplication: { type: Number, min: 1, max: 5, default: 0 },
      overallSatisfaction: { type: Number, min: 1, max: 5, default: 0 }
    }
  },
  
  // Statistiques du module
  statistics: {
    completionRate: { type: Number, min: 0, max: 100, default: 0 },
    averageGrade: { type: Number, min: 0, max: 20, default: 0 },
    passRate: { type: Number, min: 0, max: 100, default: 0 },
    dropoutRate: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  // Amélioration continue
  improvements: [{
    area: { type: String, required: true, trim: true },
    description: { type: String, required: true, trim: true },
    implementationDate: { type: Date },
    status: {
      type: String,
      enum: ['planned', 'in_progress', 'completed', 'cancelled'],
      default: 'planned'
    },
    responsiblePerson: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
moduleSchema.index({ moduleCode: 1 });
moduleSchema.index({ coordinator: 1 });
moduleSchema.index({ academicYear: 1, semester: 1 });
moduleSchema.index({ status: 1 });
moduleSchema.index({ category: 1 });
moduleSchema.index({ theme: 1 });

// Virtual pour calculer la durée totale en heures
moduleSchema.virtual('totalHours').get(function() {
  return this.duration.totalContactHours;
});

// Virtual pour vérifier si le module est complet
moduleSchema.virtual('isFull').get(function() {
  return this.capacity.current >= this.capacity.maximum;
});

// Virtual pour calculer le taux d'occupation
moduleSchema.virtual('occupancyRate').get(function() {
  return Math.round((this.capacity.current / this.capacity.maximum) * 100);
});

// Middleware pour valider les dates
moduleSchema.pre('save', function(next) {
  if (this.startDate >= this.endDate) {
    return next(new Error('End date must be after start date'));
  }
  
  // Valider que la somme des poids des méthodes d'évaluation = 100%
  if (this.assessment.methods.length > 0) {
    const totalWeight = this.assessment.methods.reduce((sum, method) => sum + method.weight, 0);
    if (totalWeight !== 100) {
      return next(new Error('Total weight of assessment methods must equal 100%'));
    }
  }
  
  next();
});

// Méthode pour calculer les notes moyennes des évaluations
moduleSchema.methods.calculateAverageRatings = function() {
  if (this.feedback.studentEvaluations.length === 0) return;
  
  const evaluations = this.feedback.studentEvaluations;
  const avgRatings = {
    contentRelevance: 0,
    instructorQuality: 0,
    learningExperience: 0,
    practicalApplication: 0,
    overallSatisfaction: 0
  };
  
  Object.keys(avgRatings).forEach(key => {
    const sum = evaluations.reduce((total, eval) => total + (eval.ratings[key] || 0), 0);
    avgRatings[key] = Math.round((sum / evaluations.length) * 10) / 10;
  });
  
  this.feedback.averageRatings = avgRatings;
  return this.save();
};

// Méthode pour obtenir les étudiants inscrits
moduleSchema.methods.getEnrolledStudents = function() {
  return mongoose.model('Enrollment').find({
    module: this._id,
    status: 'enrolled'
  }).populate('student');
};

// Méthode pour vérifier les prérequis d'un étudiant
moduleSchema.methods.checkPrerequisites = function(studentId) {
  if (this.prerequisites.length === 0) return Promise.resolve(true);
  
  const prerequisiteChecks = this.prerequisites.map(prereq => {
    return mongoose.model('Grade').findOne({
      student: studentId,
      module: prereq.module,
      finalGrade: { $gte: prereq.minimumGrade }
    });
  });
  
  return Promise.all(prerequisiteChecks).then(results => {
    return results.every(result => result !== null);
  });
};

// Méthode pour mettre à jour les statistiques
moduleSchema.methods.updateStatistics = function() {
  return mongoose.model('Enrollment').aggregate([
    { $match: { module: this._id } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]).then(results => {
    const stats = results.reduce((acc, result) => {
      acc[result._id] = result.count;
      return acc;
    }, {});
    
    const enrolled = stats.enrolled || 0;
    const completed = stats.completed || 0;
    const dropped = stats.dropped || 0;
    
    this.capacity.current = enrolled;
    this.statistics.completionRate = enrolled > 0 ? Math.round((completed / enrolled) * 100) : 0;
    this.statistics.dropoutRate = enrolled > 0 ? Math.round((dropped / enrolled) * 100) : 0;
    
    return this.save();
  });
};

module.exports = mongoose.model('Module', moduleSchema);
