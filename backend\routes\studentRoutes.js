const express = require('express');
const router = express.Router();
const Student = require('../models/Student');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/students - Obtenir tous les étudiants
router.get('/', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const { page = 1, limit = 10, cohort, enrollmentStatus, academicYear } = req.query;
    const query = {};

    if (cohort) query.cohort = cohort;
    if (enrollmentStatus) query.enrollmentStatus = enrollmentStatus;
    if (academicYear) query.academicYear = academicYear;

    const students = await Student.find(query)
      .populate('user', 'firstName lastName email phone profilePicture')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Student.countDocuments(query);

    res.json({
      students,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id - Obtenir un étudiant par ID
router.get('/:id', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id)
      .populate('user', '-password -resetPasswordToken -verificationToken')
      .populate('finalProject.supervisor', 'user academicTitle department');

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json(student);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/students - Créer un nouvel étudiant avec données utilisateur
router.post('/', authenticate, authorize('admin', 'staff'), async (req, res) => {
  let createdUser = null;

  try {
    const { firstName, lastName, email, phone, ...studentData } = req.body;

    console.log('Données reçues:', { firstName, lastName, email, phone, ...studentData });

    // 1. Créer d'abord l'utilisateur
    const User = require('../models/User');
    const userData = {
      firstName,
      lastName,
      email,
      phone,
      role: 'student',
      password: generateTemporaryPassword(),
      isActive: true
    };

    const user = new User(userData);
    await user.save();
    createdUser = user; // Garder référence pour cleanup si nécessaire
    console.log('Utilisateur créé:', user._id);

    // 2. Générer le numéro d'étudiant
    const studentNumber = await generateStudentNumber();
    console.log('Numéro étudiant généré:', studentNumber);

    // 3. Créer l'étudiant avec la référence utilisateur
    const completeStudentData = {
      ...studentData,
      user: user._id,
      studentNumber
    };

    console.log('Données étudiant complètes:', completeStudentData);

    const student = new Student(completeStudentData);
    await student.save();
    console.log('Étudiant créé:', student._id);

    // 4. Retourner l'étudiant avec les données utilisateur populées
    const populatedStudent = await Student.findById(student._id)
      .populate('user', 'firstName lastName email phone profilePicture');

    console.log('✅ Étudiant créé avec succès:', populatedStudent.studentNumber);
    res.status(201).json(populatedStudent);

  } catch (error) {
    console.error('❌ Erreur création étudiant:', error);

    // Si l'utilisateur a été créé mais pas l'étudiant, le supprimer
    if (createdUser && error.name !== 'MongoError' && error.code !== 11000) {
      try {
        await User.findByIdAndDelete(createdUser._id);
        console.log('🧹 Utilisateur orphelin supprimé:', createdUser._id);
      } catch (cleanupError) {
        console.error('⚠️ Erreur lors du nettoyage:', cleanupError);
      }
    }

    if (error.code === 11000) {
      if (error.keyPattern?.email) {
        res.status(400).json({ message: 'Cette adresse email est déjà utilisée' });
      } else {
        res.status(400).json({ message: 'Cet étudiant existe déjà' });
      }
    } else {
      res.status(400).json({ message: error.message });
    }
  }
});

// PUT /api/students/:id - Mettre à jour un étudiant
router.put('/:id', authenticate, authorize('admin', 'staff'), async (req, res) => {
  try {
    const student = await Student.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.body.updatedBy },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json(student);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// DELETE /api/students/:id - Supprimer un étudiant
router.delete('/:id', authenticate, authorize('admin'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Supprimer l'utilisateur associé si nécessaire
    if (student.user) {
      const User = require('../models/User');
      await User.findByIdAndDelete(student.user);
    }

    // Supprimer l'étudiant
    await Student.findByIdAndDelete(req.params.id);

    res.json({ message: 'Student deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id/academic-progress - Obtenir la progression académique
router.get('/:id/academic-progress', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    const progress = student.getAcademicProgress();
    const isEligible = student.isEligibleForGraduation();

    res.json({
      progressPercentage: progress,
      creditsCompleted: student.academicRecord.creditsCompleted,
      creditsRequired: student.academicRecord.creditsRequired,
      currentGPA: student.academicRecord.currentGPA,
      isEligibleForGraduation: isEligible,
      academicStanding: student.academicRecord.academicStanding
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id/attendance - Obtenir les statistiques de présence
router.get('/:id/attendance', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json({
      totalSessions: student.attendanceRecord.totalSessions,
      attendedSessions: student.attendanceRecord.attendedSessions,
      attendanceRate: student.attendancePercentage,
      absenceReasons: student.attendanceRecord.absenceReasons
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id/financial - Obtenir les informations financières
router.get('/:id/financial', async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json({
      tuitionFee: student.tuitionFee,
      remainingBalance: student.remainingBalance,
      paymentPlan: student.tuitionFee.paymentPlan
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/students/:id/notes - Ajouter une note
router.post('/:id/notes', async (req, res) => {
  try {
    const { content, category, isPrivate, author } = req.body;
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    student.notes.push({
      author,
      content,
      category: category || 'academic',
      isPrivate: isPrivate || false
    });

    await student.save();

    res.status(201).json({ message: 'Note added successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// POST /api/students/:id/documents - Ajouter un document
router.post('/:id/documents', async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    student.documents.push(req.body);
    await student.save();

    res.status(201).json({ message: 'Document added successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/students/search - Rechercher des étudiants
router.get('/search', async (req, res) => {
  try {
    const { q, cohort, program, limit = 10 } = req.query;

    if (!q) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    const query = {
      $or: [
        { studentNumber: { $regex: q, $options: 'i' } }
      ]
    };

    if (cohort) query.cohort = cohort;
    if (program) query.program = program;

    const students = await Student.find(query)
      .populate('user', 'firstName lastName email')
      .limit(parseInt(limit))
      .sort({ studentNumber: 1 });

    res.json(students);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/statistics - Obtenir les statistiques des étudiants
router.get('/statistics', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const { academicYear, cohort } = req.query;
    const matchQuery = {};

    if (academicYear) matchQuery.academicYear = academicYear;
    if (cohort) matchQuery.cohort = cohort;

    const stats = await Student.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$enrollmentStatus',
          count: { $sum: 1 },
          averageGPA: { $avg: '$academicRecord.currentGPA' }
        }
      }
    ]);

    const totalStudents = await Student.countDocuments(matchQuery);

    res.json({
      totalStudents,
      statusBreakdown: stats
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Fonctions utilitaires
const generateTemporaryPassword = () => {
  return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
};

const generateStudentNumber = async () => {
  const currentYear = new Date().getFullYear();
  const prefix = `EMBA${currentYear}`;

  // Trouver le dernier numéro d'étudiant de cette année
  const lastStudent = await Student.findOne({
    studentNumber: { $regex: `^${prefix}` }
  }).sort({ studentNumber: -1 });

  let nextNumber = 1;
  if (lastStudent) {
    const lastNumber = parseInt(lastStudent.studentNumber.replace(prefix, ''));
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
};

// Route pour obtenir la progression de candidature d'un étudiant
router.get('/:id/application-progress', authenticate, authorize('admin', 'staff'), async (req, res) => {
  try {
    const Application = require('../models/Application');
    const student = await Student.findById(req.params.id).populate('user');

    if (!student) {
      return res.status(404).json({ message: 'Étudiant non trouvé' });
    }

    // Chercher la candidature associée à cet étudiant
    const application = await Application.findOne({
      'personalInfo.email': student.user.email
    });

    if (!application) {
      return res.json({
        sections: [
          { name: 'Informations personnelles', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Formation académique', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Expérience professionnelle', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Motivation', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Documents', percentage: 0, status: 'missing', missingFields: ['Tous les documents'] }
        ]
      });
    }

    // Calculer la progression pour chaque section
    const sections = [
      {
        name: 'Informations personnelles',
        percentage: calculatePersonalInfoProgress(application.personalInfo),
        status: getProgressStatus(calculatePersonalInfoProgress(application.personalInfo)),
        missingFields: getMissingPersonalFields(application.personalInfo)
      },
      {
        name: 'Formation académique',
        percentage: calculateAcademicProgress(application.academicBackground),
        status: getProgressStatus(calculateAcademicProgress(application.academicBackground)),
        missingFields: getMissingAcademicFields(application.academicBackground)
      },
      {
        name: 'Expérience professionnelle',
        percentage: calculateWorkProgress(application.workExperience),
        status: getProgressStatus(calculateWorkProgress(application.workExperience)),
        missingFields: getMissingWorkFields(application.workExperience)
      },
      {
        name: 'Motivation',
        percentage: calculateMotivationProgress(application.motivation),
        status: getProgressStatus(calculateMotivationProgress(application.motivation)),
        missingFields: getMissingMotivationFields(application.motivation)
      },
      {
        name: 'Documents',
        percentage: calculateDocumentsProgress(application.documents),
        status: getProgressStatus(calculateDocumentsProgress(application.documents)),
        missingFields: getMissingDocumentFields(application.documents)
      }
    ];

    res.json({ sections });
  } catch (error) {
    console.error('Erreur lors du calcul de la progression:', error);
    res.status(500).json({ message: error.message });
  }
});

// Route pour obtenir l'historique des actions d'un étudiant
router.get('/:id/action-history', authenticate, authorize('admin', 'staff'), async (req, res) => {
  try {
    const Application = require('../models/Application');
    const student = await Student.findById(req.params.id).populate('user');

    if (!student) {
      return res.status(404).json({ message: 'Étudiant non trouvé' });
    }

    // Chercher la candidature associée
    const application = await Application.findOne({
      'personalInfo.email': student.user.email
    });

    const actions = [];

    // Ajouter les actions de l'étudiant
    actions.push({
      action: 'Compte créé',
      description: 'Compte étudiant créé dans le système',
      date: student.createdAt,
      performedBy: 'Système'
    });

    if (student.enrollmentDate) {
      actions.push({
        action: 'Inscription confirmée',
        description: 'Inscription au programme confirmée',
        date: student.enrollmentDate,
        performedBy: 'Administration'
      });
    }

    // Ajouter les actions de la candidature si elle existe
    if (application && application.applicationStatus.statusHistory) {
      application.applicationStatus.statusHistory.forEach(status => {
        actions.push({
          action: `Candidature ${getStatusLabel(status.status)}`,
          description: status.reason || status.comments || `Statut changé vers ${getStatusLabel(status.status)}`,
          date: status.changeDate,
          performedBy: status.changedBy || 'Administration'
        });
      });
    }

    // Trier par date décroissante
    actions.sort((a, b) => new Date(b.date) - new Date(a.date));

    res.json({ actions });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({ message: error.message });
  }
});

// Fonctions utilitaires pour calculer la progression
function calculatePersonalInfoProgress(personalInfo) {
  if (!personalInfo) return 0;
  const fields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender', 'nationality'];
  const completedFields = fields.filter(field => personalInfo[field]);
  return Math.round((completedFields.length / fields.length) * 100);
}

function calculateAcademicProgress(academicBackground) {
  if (!academicBackground || academicBackground.length === 0) return 0;
  const requiredFields = ['degree', 'institution', 'endYear'];
  const firstEducation = academicBackground[0];
  const completedFields = requiredFields.filter(field => firstEducation[field]);
  return Math.round((completedFields.length / requiredFields.length) * 100);
}

function calculateWorkProgress(workExperience) {
  if (!workExperience || workExperience.length === 0) return 0;
  const requiredFields = ['company', 'position', 'industry'];
  const firstWork = workExperience[0];
  const completedFields = requiredFields.filter(field => firstWork[field]);
  return Math.round((completedFields.length / requiredFields.length) * 100);
}

function calculateMotivationProgress(motivation) {
  if (!motivation) return 0;
  const fields = ['whyMBA', 'careerGoals'];
  const completedFields = fields.filter(field => motivation[field]);
  return Math.round((completedFields.length / fields.length) * 100);
}

function calculateDocumentsProgress(documents) {
  if (!documents || documents.length === 0) return 0;
  const requiredDocs = ['cv_resume', 'personal_statement'];
  const submittedDocs = documents.map(doc => doc.type);
  const completedDocs = requiredDocs.filter(docType => submittedDocs.includes(docType));
  return Math.round((completedDocs.length / requiredDocs.length) * 100);
}

function getProgressStatus(percentage) {
  if (percentage >= 80) return 'completed';
  if (percentage >= 40) return 'in_progress';
  return 'missing';
}

function getMissingPersonalFields(personalInfo) {
  if (!personalInfo) return ['Toutes les informations'];
  const fields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender', 'nationality'];
  return fields.filter(field => !personalInfo[field]);
}

function getMissingAcademicFields(academicBackground) {
  if (!academicBackground || academicBackground.length === 0) return ['Diplôme', 'Institution', 'Année'];
  const requiredFields = ['degree', 'institution', 'endYear'];
  const firstEducation = academicBackground[0];
  return requiredFields.filter(field => !firstEducation[field]);
}

function getMissingWorkFields(workExperience) {
  if (!workExperience || workExperience.length === 0) return ['Entreprise', 'Poste', 'Secteur'];
  const requiredFields = ['company', 'position', 'industry'];
  const firstWork = workExperience[0];
  return requiredFields.filter(field => !firstWork[field]);
}

function getMissingMotivationFields(motivation) {
  if (!motivation) return ['Motivation', 'Objectifs'];
  const fields = ['whyMBA', 'careerGoals'];
  return fields.filter(field => !motivation[field]);
}

function getMissingDocumentFields(documents) {
  if (!documents || documents.length === 0) return ['CV', 'Lettre de motivation'];
  const requiredDocs = ['cv_resume', 'personal_statement'];
  const submittedDocs = documents.map(doc => doc.type);
  return requiredDocs.filter(docType => !submittedDocs.includes(docType));
}

function getStatusLabel(status) {
  const statusLabels = {
    'submitted': 'soumise',
    'under_review': 'en révision',
    'interview_scheduled': 'entretien programmé',
    'accepted': 'acceptée',
    'rejected': 'refusée',
    'waitlisted': 'en liste d\'attente'
  };
  return statusLabels[status] || status;
}

module.exports = router;
