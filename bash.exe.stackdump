Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB917C0000 ntdll.dll
7FFB904E0000 KERNEL32.DLL
7FFB8EE60000 KERNELBASE.dll
7FFB8FEB0000 USER32.dll
7FFB8F520000 win32u.dll
7FFB906F0000 GDI32.dll
7FFB8F390000 gdi32full.dll
7FFB8EDC0000 msvcp_win.dll
7FFB8F1F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB913A0000 advapi32.dll
7FFB91160000 msvcrt.dll
7FFB91300000 sechost.dll
7FFB91650000 RPCRT4.dll
7FFB8E310000 CRYPTBASE.DLL
7FFB8F310000 bcryptPrimitives.dll
7FFB90070000 IMM32.DLL
