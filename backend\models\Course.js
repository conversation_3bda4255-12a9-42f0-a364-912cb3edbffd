const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
  // Informations de base du cours
  courseCode: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^[A-Z]{2,4}\d{3,4}$/, 'Format: ABC123 ou ABCD1234']
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // Détails académiques
  creditHours: {
    type: Number,
    required: true,
    min: 1,
    max: 10
  },
  contactHours: {
    type: Number,
    required: true,
    min: 1
  },
  level: {
    type: String,
    enum: ['Foundation', 'Core', 'Elective', 'Capstone', 'Specialization'],
    required: true
  },
  category: {
    type: String,
    enum: ['Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Leadership', 'Economics', 'Accounting', 'Information Systems', 'Human Resources', 'Entrepreneurship'],
    required: true
  },
  
  // Prérequis et corequisites
  prerequisites: [{
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    minimumGrade: {
      type: Number,
      min: 0,
      max: 20,
      default: 10
    }
  }],
  corequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  
  // Instructeur et équipe pédagogique
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor',
    required: true
  },
  coInstructors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor'
  }],
  teachingAssistants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  // Planification
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Fall', 'Spring', 'Summer', 'Intensive'],
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  
  // Horaires des sessions
  schedule: [{
    dayOfWeek: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
      required: true
    },
    startTime: {
      type: String,
      required: true,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
    },
    endTime: {
      type: String,
      required: true,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
    },
    location: {
      building: { type: String, trim: true },
      room: { type: String, trim: true },
      capacity: { type: Number, min: 1 },
      equipment: [{ type: String, trim: true }]
    },
    isOnline: { type: Boolean, default: false },
    meetingUrl: { type: String, trim: true }
  }],
  
  // Capacité et inscriptions
  capacity: {
    maximum: { type: Number, required: true, min: 1 },
    minimum: { type: Number, required: true, min: 1 },
    current: { type: Number, default: 0, min: 0 },
    waitingList: { type: Number, default: 0, min: 0 }
  },
  
  // Statut du cours
  status: {
    type: String,
    enum: ['draft', 'active', 'full', 'cancelled', 'completed', 'archived'],
    default: 'draft'
  },
  registrationStatus: {
    type: String,
    enum: ['open', 'closed', 'waitlist_only'],
    default: 'closed'
  },
  
  // Objectifs d'apprentissage
  learningObjectives: [{
    objective: { type: String, required: true, trim: true },
    bloomLevel: {
      type: String,
      enum: ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create']
    }
  }],
  
  // Contenu du cours
  syllabus: {
    overview: { type: String, trim: true },
    topics: [{
      week: { type: Number, min: 1 },
      title: { type: String, required: true, trim: true },
      description: { type: String, trim: true },
      learningOutcomes: [{ type: String, trim: true }],
      readings: [{
        type: { type: String, enum: ['Required', 'Recommended', 'Supplementary'] },
        title: { type: String, required: true, trim: true },
        author: { type: String, trim: true },
        source: { type: String, trim: true },
        pages: { type: String, trim: true },
        url: { type: String, trim: true }
      }],
      activities: [{ type: String, trim: true }]
    }],
    textbooks: [{
      title: { type: String, required: true, trim: true },
      author: { type: String, required: true, trim: true },
      edition: { type: String, trim: true },
      publisher: { type: String, trim: true },
      isbn: { type: String, trim: true },
      isRequired: { type: Boolean, default: true },
      price: { type: Number, min: 0 }
    }]
  },
  
  // Évaluation et notation
  gradingScheme: {
    components: [{
      name: {
        type: String,
        required: true,
        enum: ['Participation', 'Assignments', 'Quizzes', 'Midterm Exam', 'Final Exam', 'Project', 'Case Study', 'Presentation', 'Other']
      },
      weight: { type: Number, required: true, min: 0, max: 100 },
      description: { type: String, trim: true }
    }],
    gradingScale: [{
      grade: { type: String, required: true },
      minPercentage: { type: Number, required: true, min: 0, max: 100 },
      maxPercentage: { type: Number, required: true, min: 0, max: 100 },
      gpaPoints: { type: Number, required: true, min: 0, max: 4 }
    }],
    passingGrade: { type: Number, default: 60, min: 0, max: 100 }
  },
  
  // Politiques du cours
  policies: {
    attendancePolicy: { type: String, trim: true },
    lateSubmissionPolicy: { type: String, trim: true },
    makeupExamPolicy: { type: String, trim: true },
    academicIntegrityPolicy: { type: String, trim: true },
    accommodationsPolicy: { type: String, trim: true }
  },
  
  // Ressources et matériaux
  resources: {
    onlineResources: [{
      title: { type: String, required: true, trim: true },
      url: { type: String, required: true, trim: true },
      description: { type: String, trim: true },
      type: {
        type: String,
        enum: ['Video', 'Article', 'Website', 'Database', 'Software', 'Other']
      }
    }],
    softwareRequired: [{
      name: { type: String, required: true, trim: true },
      version: { type: String, trim: true },
      license: { type: String, trim: true },
      downloadUrl: { type: String, trim: true }
    }],
    equipmentNeeded: [{ type: String, trim: true }]
  },
  
  // Évaluations du cours
  evaluations: {
    studentFeedback: [{
      student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Student'
      },
      ratings: {
        courseContent: { type: Number, min: 1, max: 5 },
        instructorEffectiveness: { type: Number, min: 1, max: 5 },
        courseDifficulty: { type: Number, min: 1, max: 5 },
        workload: { type: Number, min: 1, max: 5 },
        overallSatisfaction: { type: Number, min: 1, max: 5 }
      },
      comments: { type: String, trim: true },
      suggestions: { type: String, trim: true },
      submittedAt: { type: Date, default: Date.now }
    }],
    averageRatings: {
      courseContent: { type: Number, min: 1, max: 5, default: 0 },
      instructorEffectiveness: { type: Number, min: 1, max: 5, default: 0 },
      courseDifficulty: { type: Number, min: 1, max: 5, default: 0 },
      workload: { type: Number, min: 1, max: 5, default: 0 },
      overallSatisfaction: { type: Number, min: 1, max: 5, default: 0 }
    }
  },
  
  // Statistiques du cours
  statistics: {
    enrollmentHistory: [{
      date: { type: Date, default: Date.now },
      enrolled: { type: Number, default: 0 },
      dropped: { type: Number, default: 0 },
      completed: { type: Number, default: 0 }
    }],
    gradeDistribution: [{
      grade: { type: String, required: true },
      count: { type: Number, default: 0 },
      percentage: { type: Number, default: 0 }
    }],
    averageGrade: { type: Number, min: 0, max: 20, default: 0 },
    passRate: { type: Number, min: 0, max: 100, default: 0 },
    completionRate: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModified: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
courseSchema.index({ courseCode: 1 });
courseSchema.index({ instructor: 1 });
courseSchema.index({ academicYear: 1, semester: 1 });
courseSchema.index({ status: 1 });
courseSchema.index({ category: 1 });
courseSchema.index({ level: 1 });

// Virtual pour calculer la durée du cours en semaines
courseSchema.virtual('durationWeeks').get(function() {
  const diffTime = Math.abs(this.endDate - this.startDate);
  const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
  return diffWeeks;
});

// Virtual pour vérifier si le cours est complet
courseSchema.virtual('isFull').get(function() {
  return this.capacity.current >= this.capacity.maximum;
});

// Virtual pour calculer le taux d'occupation
courseSchema.virtual('occupancyRate').get(function() {
  return Math.round((this.capacity.current / this.capacity.maximum) * 100);
});

// Virtual pour vérifier si les inscriptions sont ouvertes
courseSchema.virtual('canEnroll').get(function() {
  return this.registrationStatus === 'open' && !this.isFull;
});

// Middleware pour valider les dates
courseSchema.pre('save', function(next) {
  if (this.startDate >= this.endDate) {
    return next(new Error('End date must be after start date'));
  }
  
  // Valider que la somme des poids des composants d'évaluation = 100%
  if (this.gradingScheme.components.length > 0) {
    const totalWeight = this.gradingScheme.components.reduce((sum, comp) => sum + comp.weight, 0);
    if (totalWeight !== 100) {
      return next(new Error('Total weight of grading components must equal 100%'));
    }
  }
  
  next();
});

// Middleware pour mettre à jour lastModified
courseSchema.pre('save', function(next) {
  this.lastModified = new Date();
  next();
});

// Méthode pour calculer les notes moyennes des évaluations
courseSchema.methods.calculateAverageRatings = function() {
  if (this.evaluations.studentFeedback.length === 0) return;
  
  const ratings = this.evaluations.studentFeedback;
  const avgRatings = {
    courseContent: 0,
    instructorEffectiveness: 0,
    courseDifficulty: 0,
    workload: 0,
    overallSatisfaction: 0
  };
  
  Object.keys(avgRatings).forEach(key => {
    const sum = ratings.reduce((total, feedback) => total + (feedback.ratings[key] || 0), 0);
    avgRatings[key] = Math.round((sum / ratings.length) * 10) / 10;
  });
  
  this.evaluations.averageRatings = avgRatings;
  return this.save();
};

// Méthode pour vérifier si un étudiant peut s'inscrire
courseSchema.methods.canStudentEnroll = function(studentId) {
  return mongoose.model('Enrollment').findOne({
    student: studentId,
    course: this._id,
    status: { $in: ['enrolled', 'completed'] }
  }).then(enrollment => !enrollment);
};

// Méthode pour obtenir les étudiants inscrits
courseSchema.methods.getEnrolledStudents = function() {
  return mongoose.model('Enrollment').find({
    course: this._id,
    status: 'enrolled'
  }).populate('student');
};

// Méthode pour mettre à jour les statistiques
courseSchema.methods.updateStatistics = function() {
  return mongoose.model('Enrollment').aggregate([
    { $match: { course: this._id } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]).then(results => {
    const stats = results.reduce((acc, result) => {
      acc[result._id] = result.count;
      return acc;
    }, {});
    
    this.capacity.current = stats.enrolled || 0;
    this.statistics.completionRate = stats.enrolled > 0 ? 
      Math.round(((stats.completed || 0) / stats.enrolled) * 100) : 0;
    
    return this.save();
  });
};

module.exports = mongoose.model('Course', courseSchema);
