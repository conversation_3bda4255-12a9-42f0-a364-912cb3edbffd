const express = require('express');
const router = express.Router();
const Module = require('../models/Module');

// CRUD de base pour les modules
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, category, status } = req.query;
    const query = {};
    if (category) query.category = category;
    if (status) query.status = status;
    
    const modules = await Module.find(query)
      .populate('coordinator', 'user academicTitle department')
      .populate('courses.course', 'courseCode title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Module.countDocuments(query);
    res.json({ modules, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const module = await <PERSON>dule.findById(req.params.id)
      .populate('coordinator', 'user academicTitle department')
      .populate('courses.course', 'courseCode title creditHours')
      .populate('prerequisites.module', 'moduleCode title');
    
    if (!module) return res.status(404).json({ message: 'Module not found' });
    res.json(module);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const module = new Module(req.body);
    await module.save();
    res.status(201).json(module);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const module = await Module.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!module) return res.status(404).json({ message: 'Module not found' });
    res.json(module);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.delete('/:id', async (req, res) => {
  try {
    const module = await Module.findByIdAndDelete(req.params.id);
    if (!module) return res.status(404).json({ message: 'Module not found' });
    res.json({ message: 'Module deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
