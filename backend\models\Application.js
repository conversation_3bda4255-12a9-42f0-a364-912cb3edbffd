const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
  // Numéro de candidature unique
  applicationNumber: {
    type: String,
    unique: true,
    trim: true
  },
  
  // Informations personnelles du candidat
  personalInfo: {
    firstName: { type: String, required: true, trim: true, maxlength: 50 },
    lastName: { type: String, required: true, trim: true, maxlength: 50 },
    middleName: { type: String, trim: true, maxlength: 50 },
    dateOfBirth: { type: Date, required: true },
    gender: {
      type: String,
      enum: ['Male', 'Female', 'Other'],
      required: true
    },
    nationality: { type: String, required: true, trim: true },
    maritalStatus: {
      type: String,
      enum: ['Single', 'Married', 'Divorced', 'Widowed', 'Other']
    },
    
    // Contact
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    phone: {
      type: String,
      required: true,
      trim: true,
      match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
    },
    alternatePhone: { type: String, trim: true },
    
    // Adresse
    address: {
      street: { type: String, required: true, trim: true },
      city: { type: String, required: true, trim: true },
      state: { type: String, trim: true },
      zipCode: { type: String, required: true, trim: true },
      country: { type: String, required: true, trim: true, default: 'Tunisia' }
    },
    
    // Photo
    profilePhoto: {
      filename: { type: String },
      path: { type: String },
      uploadDate: { type: Date }
    }
  },
  
  // Programme demandé
  programInfo: {
    program: {
      type: String,
      enum: ['EMBA', 'Executive MBA', 'Part-time MBA', 'Full-time MBA'],
      required: true,
      default: 'EMBA'
    },
    specialization: {
      type: String,
      enum: ['General Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Digital Transformation', 'Entrepreneurship']
    },
    intakeYear: {
      type: String,
      required: true,
      match: [/^\d{4}$/, 'Format: YYYY']
    },
    intakeSemester: {
      type: String,
      enum: ['Fall', 'Spring', 'Summer'],
      required: true
    },
    studyMode: {
      type: String,
      enum: ['full_time', 'part_time', 'weekend', 'evening'],
      default: 'part_time'
    }
  },
  
  // Formation académique
  academicBackground: [{
    degree: {
      type: String,
      required: true,
      enum: ['High School', 'Bachelor', 'Master', 'PhD', 'Professional Certificate', 'Other']
    },
    fieldOfStudy: { type: String, required: true, trim: true },
    institution: { type: String, required: true, trim: true },
    country: { type: String, required: true, trim: true },
    startYear: { type: Number, required: true, min: 1950 },
    endYear: { type: Number, required: true, min: 1950 },
    gpa: { type: Number, min: 0, max: 4 },
    gradeSystem: { type: String, trim: true },
    isHighestDegree: { type: Boolean, default: false },
    transcriptSubmitted: { type: Boolean, default: false }
  }],
  
  // Expérience professionnelle
  workExperience: [{
    company: { type: String, required: true, trim: true },
    position: { type: String, required: true, trim: true },
    industry: { type: String, trim: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date },
    isCurrent: { type: Boolean, default: false },
    responsibilities: { type: String, trim: true, maxlength: 1000 },
    achievements: [{ type: String, trim: true }],
    salary: { type: Number, min: 0 },
    managementLevel: {
      type: String,
      enum: ['Entry Level', 'Mid-Level', 'Senior Level', 'Executive', 'C-Level']
    },
    teamSize: { type: Number, min: 0 },
    location: {
      city: { type: String, trim: true },
      country: { type: String, trim: true }
    }
  }],
  
  // Expérience de leadership et réalisations
  leadership: {
    totalYearsExperience: { type: Number, min: 0 },
    managementExperience: { type: Number, min: 0 },
    leadershipRoles: [{
      role: { type: String, required: true, trim: true },
      organization: { type: String, required: true, trim: true },
      duration: { type: String, trim: true },
      description: { type: String, trim: true, maxlength: 500 }
    }],
    majorAchievements: [{ type: String, trim: true, maxlength: 500 }],
    awards: [{
      title: { type: String, required: true, trim: true },
      organization: { type: String, required: true, trim: true },
      year: { type: Number, min: 1950 },
      description: { type: String, trim: true }
    }]
  },
  
  // Motivations et objectifs
  motivation: {
    whyMBA: { type: String, required: true, trim: true, maxlength: 2000 },
    careerGoals: { type: String, required: true, trim: true, maxlength: 1500 },
    whyThisSchool: { type: String, required: true, trim: true, maxlength: 1000 },
    contribution: { type: String, trim: true, maxlength: 1000 },
    postMBAPlans: { type: String, trim: true, maxlength: 1000 }
  },
  
  // Compétences et langues
  skills: {
    technicalSkills: [{ type: String, trim: true }],
    softSkills: [{ type: String, trim: true }],
    languages: [{
      language: { type: String, required: true, trim: true },
      proficiency: {
        type: String,
        enum: ['Beginner', 'Intermediate', 'Advanced', 'Native'],
        required: true
      },
      certified: { type: Boolean, default: false },
      certificationName: { type: String, trim: true }
    }],
    certifications: [{
      name: { type: String, required: true, trim: true },
      issuingOrganization: { type: String, required: true, trim: true },
      issueDate: { type: Date },
      expirationDate: { type: Date },
      credentialId: { type: String, trim: true }
    }]
  },
  
  // Tests standardisés
  standardizedTests: [{
    testType: {
      type: String,
      enum: ['GMAT', 'GRE', 'TOEFL', 'IELTS', 'Other'],
      required: true
    },
    score: { type: Number, required: true, min: 0 },
    maxScore: { type: Number, required: true, min: 0 },
    testDate: { type: Date, required: true },
    validUntil: { type: Date },
    reportSubmitted: { type: Boolean, default: false }
  }],
  
  // Recommandations
  recommendations: [{
    recommenderName: { type: String, required: true, trim: true },
    recommenderTitle: { type: String, required: true, trim: true },
    recommenderOrganization: { type: String, required: true, trim: true },
    recommenderEmail: { type: String, required: true, lowercase: true, trim: true },
    recommenderPhone: { type: String, trim: true },
    relationship: { type: String, required: true, trim: true },
    relationshipDuration: { type: String, trim: true },
    letterSubmitted: { type: Boolean, default: false },
    submissionDate: { type: Date },
    remindersSent: { type: Number, default: 0 },
    lastReminderDate: { type: Date }
  }],
  
  // Documents soumis
  documents: [{
    type: {
      type: String,
      enum: [
        'transcript', 'diploma', 'cv_resume', 'recommendation_letter',
        'personal_statement', 'test_scores', 'passport_copy', 'photo',
        'work_certificate', 'salary_certificate', 'other'
      ],
      required: true
    },
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    path: { type: String, required: true },
    size: { type: Number },
    mimeType: { type: String },
    uploadDate: { type: Date, default: Date.now },
    isVerified: { type: Boolean, default: false },
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    verificationDate: { type: Date },
    verificationNotes: { type: String, trim: true }
  }],
  
  // Financement
  financing: {
    fundingSource: {
      type: String,
      enum: ['self_funded', 'employer_sponsored', 'scholarship', 'loan', 'mixed'],
      required: true
    },
    employerSupport: {
      hasSupport: { type: Boolean, default: false },
      supportType: {
        type: String,
        enum: ['full_tuition', 'partial_tuition', 'time_off', 'other']
      },
      supportAmount: { type: Number, min: 0 },
      supportLetter: { type: Boolean, default: false }
    },
    scholarshipApplication: {
      applied: { type: Boolean, default: false },
      scholarshipType: { type: String, trim: true },
      amount: { type: Number, min: 0 },
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'not_applied'],
        default: 'not_applied'
      }
    }
  },
  
  // Statut de la candidature
  applicationStatus: {
    status: {
      type: String,
      enum: [
        'draft', 'submitted', 'under_review', 'interview_scheduled',
        'interview_completed', 'pending_decision', 'accepted',
        'rejected', 'waitlisted', 'deferred', 'withdrawn'
      ],
      default: 'draft'
    },
    submissionDate: { type: Date },
    reviewStartDate: { type: Date },
    decisionDate: { type: Date },
    decisionBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    decisionReason: { type: String, trim: true },
    
    // Historique des changements de statut
    statusHistory: [{
      status: {
        type: String,
        enum: [
          'draft', 'submitted', 'under_review', 'interview_scheduled',
          'interview_completed', 'pending_decision', 'accepted',
          'rejected', 'waitlisted', 'deferred', 'withdrawn'
        ],
        required: true
      },
      changedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      changeDate: { type: Date, default: Date.now },
      reason: { type: String, trim: true },
      comments: { type: String, trim: true }
    }]
  },
  
  // Processus d'entretien
  interview: {
    isRequired: { type: Boolean, default: true },
    scheduledDate: { type: Date },
    scheduledTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
    duration: { type: Number, min: 15, default: 60 }, // en minutes
    format: {
      type: String,
      enum: ['in_person', 'video_call', 'phone_call'],
      default: 'video_call'
    },
    location: { type: String, trim: true },
    meetingUrl: { type: String, trim: true },
    
    // Panel d'entretien
    interviewers: [{
      interviewer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      role: { type: String, trim: true },
      isLead: { type: Boolean, default: false }
    }],
    
    // Évaluation
    evaluation: {
      completed: { type: Boolean, default: false },
      completedDate: { type: Date },
      scores: {
        communication: { type: Number, min: 1, max: 10 },
        leadership: { type: Number, min: 1, max: 10 },
        motivation: { type: Number, min: 1, max: 10 },
        experience: { type: Number, min: 1, max: 10 },
        fitForProgram: { type: Number, min: 1, max: 10 },
        overallScore: { type: Number, min: 1, max: 10 }
      },
      strengths: [{ type: String, trim: true }],
      concerns: [{ type: String, trim: true }],
      recommendation: {
        type: String,
        enum: ['strongly_recommend', 'recommend', 'neutral', 'not_recommend', 'strongly_not_recommend']
      },
      comments: { type: String, trim: true },
      evaluatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  
  // Évaluation globale
  evaluation: {
    academicScore: { type: Number, min: 0, max: 100 },
    experienceScore: { type: Number, min: 0, max: 100 },
    leadershipScore: { type: Number, min: 0, max: 100 },
    motivationScore: { type: Number, min: 0, max: 100 },
    overallScore: { type: Number, min: 0, max: 100 },
    
    evaluatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    evaluationDate: { type: Date },
    evaluationNotes: { type: String, trim: true }
  },
  
  // Communications
  communications: [{
    type: {
      type: String,
      enum: ['email', 'phone', 'meeting', 'letter', 'other']
    },
    direction: {
      type: String,
      enum: ['inbound', 'outbound']
    },
    subject: { type: String, trim: true },
    content: { type: String, trim: true },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    sentTo: { type: String, trim: true },
    sentDate: { type: Date, default: Date.now },
    isRead: { type: Boolean, default: false },
    readDate: { type: Date }
  }],
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
applicationSchema.index({ applicationNumber: 1 });
applicationSchema.index({ 'personalInfo.email': 1 });
applicationSchema.index({ 'applicationStatus.status': 1 });
applicationSchema.index({ 'programInfo.intakeYear': 1, 'programInfo.intakeSemester': 1 });
applicationSchema.index({ createdAt: -1 });

// Virtual pour le nom complet
applicationSchema.virtual('fullName').get(function() {
  return `${this.personalInfo.firstName} ${this.personalInfo.lastName}`;
});

// Virtual pour calculer l'âge
applicationSchema.virtual('age').get(function() {
  if (!this.personalInfo.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.personalInfo.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual pour calculer l'expérience totale
applicationSchema.virtual('totalExperience').get(function() {
  let totalMonths = 0;
  this.workExperience.forEach(exp => {
    const endDate = exp.endDate || new Date();
    const months = (endDate.getFullYear() - exp.startDate.getFullYear()) * 12 + 
                   (endDate.getMonth() - exp.startDate.getMonth());
    totalMonths += months;
  });
  return Math.round(totalMonths / 12 * 10) / 10; // années avec 1 décimale
});

// Middleware pour générer automatiquement le numéro de candidature
applicationSchema.pre('save', async function(next) {
  if (!this.applicationNumber) {
    const year = this.programInfo.intakeYear;
    const semester = this.programInfo.intakeSemester.charAt(0); // F, S, ou Su
    const count = await this.constructor.countDocuments({
      applicationNumber: new RegExp(`^APP${year}${semester}`)
    });
    this.applicationNumber = `APP${year}${semester}${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Middleware pour enregistrer l'historique des changements de statut
applicationSchema.pre('save', function(next) {
  if (this.isModified('applicationStatus.status') && !this.isNew) {
    this.applicationStatus.statusHistory.push({
      status: this.applicationStatus.status,
      changedBy: this.updatedBy,
      changeDate: new Date()
    });
  }
  next();
});

// Méthode pour soumettre la candidature
applicationSchema.methods.submit = function(submittedBy) {
  this.applicationStatus.status = 'submitted';
  this.applicationStatus.submissionDate = new Date();
  this.updatedBy = submittedBy;
  return this.save();
};

// Méthode pour accepter la candidature
applicationSchema.methods.accept = function(acceptedBy, reason) {
  this.applicationStatus.status = 'accepted';
  this.applicationStatus.decisionDate = new Date();
  this.applicationStatus.decisionBy = acceptedBy;
  this.applicationStatus.decisionReason = reason;
  return this.save();
};

// Méthode pour rejeter la candidature
applicationSchema.methods.reject = function(rejectedBy, reason) {
  this.applicationStatus.status = 'rejected';
  this.applicationStatus.decisionDate = new Date();
  this.applicationStatus.decisionBy = rejectedBy;
  this.applicationStatus.decisionReason = reason;
  return this.save();
};

// Méthode pour programmer un entretien
applicationSchema.methods.scheduleInterview = function(date, time, interviewers, scheduledBy) {
  this.interview.scheduledDate = date;
  this.interview.scheduledTime = time;
  this.interview.interviewers = interviewers.map(interviewer => ({
    interviewer: interviewer.id,
    role: interviewer.role,
    isLead: interviewer.isLead || false
  }));
  this.applicationStatus.status = 'interview_scheduled';
  this.updatedBy = scheduledBy;
  return this.save();
};

// Méthode pour vérifier si la candidature est complète
applicationSchema.methods.isComplete = function() {
  const requiredDocuments = ['transcript', 'cv_resume', 'personal_statement'];
  const submittedDocuments = this.documents.map(doc => doc.type);
  
  const hasRequiredDocs = requiredDocuments.every(docType => 
    submittedDocuments.includes(docType)
  );
  
  const hasRecommendations = this.recommendations.length >= 2 &&
    this.recommendations.filter(rec => rec.letterSubmitted).length >= 2;
  
  return hasRequiredDocs && hasRecommendations && 
         this.personalInfo.firstName && this.personalInfo.lastName &&
         this.personalInfo.email && this.motivation.whyMBA;
};

// Méthode pour calculer le score global
applicationSchema.methods.calculateOverallScore = function() {
  const scores = this.evaluation;
  if (!scores.academicScore || !scores.experienceScore || 
      !scores.leadershipScore || !scores.motivationScore) {
    return 0;
  }
  
  // Pondération : académique 25%, expérience 35%, leadership 25%, motivation 15%
  const overallScore = (scores.academicScore * 0.25) + 
                      (scores.experienceScore * 0.35) + 
                      (scores.leadershipScore * 0.25) + 
                      (scores.motivationScore * 0.15);
  
  this.evaluation.overallScore = Math.round(overallScore);
  return this.evaluation.overallScore;
};

// Méthode statique pour obtenir les statistiques des candidatures
applicationSchema.statics.getApplicationStatistics = function(filters = {}) {
  const matchConditions = {};
  
  if (filters.intakeYear) matchConditions['programInfo.intakeYear'] = filters.intakeYear;
  if (filters.program) matchConditions['programInfo.program'] = filters.program;
  if (filters.status) matchConditions['applicationStatus.status'] = filters.status;
  
  return this.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: '$applicationStatus.status',
        count: { $sum: 1 },
        averageScore: { $avg: '$evaluation.overallScore' }
      }
    },
    {
      $group: {
        _id: null,
        totalApplications: { $sum: '$count' },
        statusBreakdown: {
          $push: {
            status: '$_id',
            count: '$count',
            averageScore: '$averageScore'
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Application', applicationSchema);
