import React, { useState, useEffect } from 'react';
import {
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  PhoneIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  UserIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';
import Modal from '../components/Modal';

const Applications = () => {
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionLoading, setActionLoading] = useState(null);

  // Charger les candidatures
  const loadApplications = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('http://localhost:5000/api/applications');
      const data = await response.json();
      
      if (response.ok) {
        setApplications(data.applications || []);
      } else {
        throw new Error(data.message || 'Erreur lors du chargement');
      }
    } catch (err) {
      console.error('Erreur lors du chargement des candidatures:', err);
      setError('Impossible de charger les candidatures. Vérifiez votre connexion.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApplications();
  }, []);

  // Filtrer les candidatures
  const filteredApplications = applications.filter(app => {
    const matchesSearch = 
      app.personalInfo?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.personalInfo?.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.personalInfo?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.applicationNumber?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || app.applicationStatus?.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Actions sur les candidatures
  const handleAction = async (applicationId, action, reason = '') => {
    setActionLoading(applicationId);
    try {
      const response = await fetch(`http://localhost:5000/api/applications/${applicationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: action,
          reason: reason
        })
      });

      if (response.ok) {
        await loadApplications(); // Recharger les données
        alert(`Candidature ${action === 'accepted' ? 'acceptée' : action === 'rejected' ? 'refusée' : 'mise à jour'} avec succès !`);
        
        // Si acceptée, créer le compte étudiant
        if (action === 'accepted') {
          await createStudentAccount(applicationId);
        }
      } else {
        throw new Error('Erreur lors de la mise à jour');
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur lors de la mise à jour de la candidature');
    } finally {
      setActionLoading(null);
    }
  };

  // Créer un compte étudiant automatiquement
  const createStudentAccount = async (applicationId) => {
    try {
      const response = await fetch(`http://localhost:5000/api/applications/${applicationId}/create-student`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Compte étudiant créé ! Email envoyé à ${result.email} avec les identifiants.`);
      }
    } catch (error) {
      console.error('Erreur lors de la création du compte étudiant:', error);
    }
  };

  // Ouvrir les détails d'une candidature
  const openApplicationDetails = (application) => {
    setSelectedApplication(application);
    setIsModalOpen(true);
  };

  // Statut avec couleurs
  const getStatusBadge = (status) => {
    const statusConfig = {
      'submitted': { label: 'Soumise', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' },
      'under_review': { label: 'En révision', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' },
      'interview_scheduled': { label: 'Entretien programmé', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' },
      'accepted': { label: 'Acceptée', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' },
      'rejected': { label: 'Refusée', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' },
      'waitlisted': { label: 'Liste d\'attente', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300' }
    };

    const config = statusConfig[status] || statusConfig['submitted'];
    return (
      <span className={cn('px-2 py-1 text-xs font-medium rounded-full', config.color)}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
        <p className="text-red-800 dark:text-red-200">{error}</p>
        <button 
          onClick={loadApplications}
          className="mt-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Candidatures</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Gérez les candidatures au programme EMBA ({filteredApplications.length} candidatures)
          </p>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher par nom, email ou numéro..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <div className="relative">
          <FunnelIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="submitted">Soumises</option>
            <option value="under_review">En révision</option>
            <option value="interview_scheduled">Entretien programmé</option>
            <option value="accepted">Acceptées</option>
            <option value="rejected">Refusées</option>
            <option value="waitlisted">Liste d'attente</option>
          </select>
        </div>
      </div>

      {/* Liste des candidatures */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
        {filteredApplications.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || statusFilter !== 'all' ? 'Aucune candidature trouvée avec ces critères' : 'Aucune candidature pour le moment'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Candidat
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date de soumission
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredApplications.map((application) => (
                  <tr key={application._id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600 dark:text-primary-300">
                              {application.personalInfo?.firstName?.[0]}{application.personalInfo?.lastName?.[0]}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {application.personalInfo?.firstName} {application.personalInfo?.lastName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            #{application.applicationNumber}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {application.personalInfo?.email}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {application.personalInfo?.phone}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(application.applicationStatus?.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(application.applicationStatus?.submissionDate).toLocaleDateString('fr-FR')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => openApplicationDetails(application)}
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                          title="Voir les détails"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        
                        {application.applicationStatus?.status === 'submitted' && (
                          <>
                            <button
                              onClick={() => handleAction(application._id, 'accepted')}
                              disabled={actionLoading === application._id}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 disabled:opacity-50"
                              title="Accepter"
                            >
                              <CheckIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => {
                                const reason = prompt('Raison du refus (optionnel):');
                                if (reason !== null) {
                                  handleAction(application._id, 'rejected', reason);
                                }
                              }}
                              disabled={actionLoading === application._id}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                              title="Refuser"
                            >
                              <XMarkIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleAction(application._id, 'interview_scheduled')}
                              disabled={actionLoading === application._id}
                              className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 disabled:opacity-50"
                              title="Programmer un entretien"
                            >
                              <CalendarDaysIcon className="h-5 w-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal des détails */}
      {selectedApplication && (
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={`Candidature #${selectedApplication.applicationNumber}`}
          size="xl"
        >
          <ApplicationDetails application={selectedApplication} />
        </Modal>
      )}
    </div>
  );
};

// Composant pour afficher les détails d'une candidature
const ApplicationDetails = ({ application }) => {
  return (
    <div className="space-y-6">
      {/* Informations personnelles */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <UserIcon className="h-5 w-5 mr-2" />
          Informations personnelles
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Nom complet:</span>
            <p className="text-gray-900 dark:text-white">
              {application.personalInfo?.firstName} {application.personalInfo?.lastName}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Email:</span>
            <p className="text-gray-900 dark:text-white">{application.personalInfo?.email}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Téléphone:</span>
            <p className="text-gray-900 dark:text-white">{application.personalInfo?.phone}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Date de naissance:</span>
            <p className="text-gray-900 dark:text-white">
              {application.personalInfo?.dateOfBirth ? 
                new Date(application.personalInfo.dateOfBirth).toLocaleDateString('fr-FR') : 'N/A'}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Nationalité:</span>
            <p className="text-gray-900 dark:text-white">{application.personalInfo?.nationality}</p>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Genre:</span>
            <p className="text-gray-900 dark:text-white">{application.personalInfo?.gender}</p>
          </div>
        </div>
      </div>

      {/* Formation académique */}
      {application.academicBackground && application.academicBackground.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Formation académique
          </h3>
          {application.academicBackground.map((edu, index) => (
            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Diplôme:</span>
                  <p className="text-gray-900 dark:text-white">{edu.degree}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Institution:</span>
                  <p className="text-gray-900 dark:text-white">{edu.institution}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Année de fin:</span>
                  <p className="text-gray-900 dark:text-white">{edu.endYear}</p>
                </div>
                {edu.gpa && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">GPA:</span>
                    <p className="text-gray-900 dark:text-white">{edu.gpa}/4.0</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Expérience professionnelle */}
      {application.workExperience && application.workExperience.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <BriefcaseIcon className="h-5 w-5 mr-2" />
            Expérience professionnelle
          </h3>
          {application.workExperience.map((work, index) => (
            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Poste:</span>
                  <p className="text-gray-900 dark:text-white">{work.position}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Entreprise:</span>
                  <p className="text-gray-900 dark:text-white">{work.company}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Secteur:</span>
                  <p className="text-gray-900 dark:text-white">{work.industry}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Taille d'équipe:</span>
                  <p className="text-gray-900 dark:text-white">{work.teamSize} personnes</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Motivation */}
      {application.motivation && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Motivation
          </h3>
          <div className="space-y-4">
            {application.motivation.whyMBA && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Pourquoi l'EMBA:</span>
                <p className="text-gray-900 dark:text-white mt-1 text-sm leading-relaxed">
                  {application.motivation.whyMBA}
                </p>
              </div>
            )}
            {application.motivation.careerGoals && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Objectifs de carrière:</span>
                <p className="text-gray-900 dark:text-white mt-1 text-sm leading-relaxed">
                  {application.motivation.careerGoals}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Documents */}
      {application.documents && application.documents.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Documents soumis
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {application.documents.map((doc, index) => (
              <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {doc.originalName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {doc.type} • {(doc.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Applications;
