const express = require('express');
const router = express.Router();
const Payment = require('../models/Payment');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, student, status, paymentType } = req.query;
    const query = {};
    if (student) query.student = student;
    if (status) query.status = status;
    if (paymentType) query.paymentType = paymentType;
    
    const payments = await Payment.find(query)
      .populate('student', 'studentNumber user')
      .populate('enrollment', 'enrollmentNumber')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Payment.countDocuments(query);
    res.json({ payments, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('student', 'studentNumber user')
      .populate('enrollment', 'enrollmentNumber');
    
    if (!payment) return res.status(404).json({ message: 'Payment not found' });
    res.json(payment);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const payment = new Payment(req.body);
    await payment.save();
    res.status(201).json(payment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const payment = await Payment.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!payment) return res.status(404).json({ message: 'Payment not found' });
    res.json(payment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/process', async (req, res) => {
  try {
    const { paymentData, processedBy } = req.body;
    const payment = await Payment.findById(req.params.id);
    
    if (!payment) return res.status(404).json({ message: 'Payment not found' });
    
    await payment.processPayment(paymentData, processedBy);
    res.json({ message: 'Payment processed successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/overdue', async (req, res) => {
  try {
    const { daysOverdue = 0 } = req.query;
    const overduePayments = await Payment.getOverduePayments(parseInt(daysOverdue));
    res.json(overduePayments);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
