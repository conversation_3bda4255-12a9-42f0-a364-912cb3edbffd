const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const router = express.Router();
const User = require('../models/User');
const { authenticate, authorize } = require('../middleware/auth');

// @route   POST /api/auth/register
// @desc    Inscription d'un nouvel utilisateur
// @access  Public
router.post('/register', async (req, res) => {
  try {
    const { firstName, lastName, email, password, phone, role = 'student' } = req.body;

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({
        message: 'Un utilisateur avec cette adresse email existe déjà'
      });
    }

    // Valider la force du mot de passe
    if (password.length < 6) {
      return res.status(400).json({
        message: 'Le mot de passe doit contenir au moins 6 caractères'
      });
    }

    // Créer le nouvel utilisateur
    const userData = {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.toLowerCase().trim(),
      password, // Le hachage se fait automatiquement via le middleware pre('save')
      phone: phone?.trim(),
      role,
      isActive: true,
      isVerified: false,
      verificationToken: crypto.randomBytes(32).toString('hex')
    };

    const user = new User(userData);
    await user.save();

    // Générer le token JWT
    const token = jwt.sign(
      {
        id: user._id,
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRE || '7d' }
    );

    // Retourner les informations utilisateur (sans le mot de passe)
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.verificationToken;

    res.status(201).json({
      message: 'Inscription réussie',
      token,
      user: userResponse
    });

  } catch (error) {
    console.error('Erreur lors de l\'inscription:', error);
    res.status(500).json({
      message: 'Erreur serveur lors de l\'inscription'
    });
  }
});

// @route   POST /api/auth/login
// @desc    Connexion utilisateur
// @access  Public
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    console.log('🔐 Tentative de connexion:', { email, password: '***' });

    // Validation des champs requis
    if (!email || !password) {
      console.log('❌ Champs manquants');
      return res.status(400).json({
        message: 'Email et mot de passe sont requis'
      });
    }

    // Trouver l'utilisateur par email
    const user = await User.findOne({ email: email.toLowerCase() });
    console.log('👤 Utilisateur trouvé:', user ? `${user.firstName} ${user.lastName} (${user.email})` : 'Aucun');

    if (!user) {
      console.log('❌ Utilisateur non trouvé pour:', email);
      return res.status(401).json({
        message: 'Identifiants invalides'
      });
    }

    // Vérifier si le compte est actif
    console.log('🔍 Statut du compte:', { isActive: user.isActive, isLocked: user.isLocked });

    if (!user.isActive) {
      console.log('❌ Compte désactivé');
      return res.status(401).json({
        message: 'Compte désactivé. Contactez l\'administrateur.'
      });
    }

    // Vérifier si le compte est verrouillé
    if (user.isLocked) {
      console.log('❌ Compte verrouillé');
      return res.status(401).json({
        message: 'Compte temporairement verrouillé. Réessayez plus tard.'
      });
    }

    // Vérifier le mot de passe
    console.log('🔑 Vérification du mot de passe...');
    const isPasswordValid = await user.comparePassword(password);
    console.log('🔑 Mot de passe valide:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('❌ Mot de passe incorrect pour:', email);
      // Incrémenter les tentatives de connexion échouées
      await user.incLoginAttempts();
      return res.status(401).json({
        message: 'Identifiants invalides'
      });
    }

    // Réinitialiser les tentatives de connexion et mettre à jour la dernière connexion
    await User.findByIdAndUpdate(user._id, {
      $unset: { loginAttempts: 1, lockUntil: 1 },
      $set: { lastLogin: new Date() }
    });

    // Générer le token JWT
    const token = jwt.sign(
      {
        id: user._id,
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRE || '7d' }
    );

    // Retourner les informations utilisateur (sans le mot de passe)
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.verificationToken;
    delete userResponse.resetPasswordToken;

    res.json({
      message: 'Connexion réussie',
      token,
      user: userResponse
    });

  } catch (error) {
    console.error('Erreur lors de la connexion:', error);
    res.status(500).json({
      message: 'Erreur serveur lors de la connexion'
    });
  }
});

// @route   GET /api/auth/me
// @desc    Obtenir les informations de l'utilisateur connecté
// @access  Private
router.get('/me', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password -verificationToken -resetPasswordToken');

    if (!user) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    res.json(user);
  } catch (error) {
    console.error('Erreur lors de la récupération du profil:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   PUT /api/auth/profile
// @desc    Mettre à jour le profil utilisateur
// @access  Private
router.put('/profile', authenticate, async (req, res) => {
  try {
    const { firstName, lastName, phone, preferences } = req.body;

    const updateData = {};
    if (firstName) updateData.firstName = firstName.trim();
    if (lastName) updateData.lastName = lastName.trim();
    if (phone) updateData.phone = phone.trim();
    if (preferences) updateData.preferences = { ...updateData.preferences, ...preferences };

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -verificationToken -resetPasswordToken');

    res.json({
      message: 'Profil mis à jour avec succès',
      user
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   POST /api/auth/change-password
// @desc    Changer le mot de passe
// @access  Private
router.post('/change-password', authenticate, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        message: 'Mot de passe actuel et nouveau mot de passe requis'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        message: 'Le nouveau mot de passe doit contenir au moins 6 caractères'
      });
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ message: 'Utilisateur non trouvé' });
    }

    // Vérifier le mot de passe actuel
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(401).json({
        message: 'Mot de passe actuel incorrect'
      });
    }

    // Mettre à jour le mot de passe
    user.password = newPassword;
    await user.save();

    res.json({ message: 'Mot de passe changé avec succès' });

  } catch (error) {
    console.error('Erreur lors du changement de mot de passe:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
});

// @route   POST /api/auth/logout
// @desc    Déconnexion (côté client principalement)
// @access  Private
router.post('/logout', authenticate, (req, res) => {
  // Avec JWT, la déconnexion se fait principalement côté client
  // en supprimant le token du localStorage
  res.json({ message: 'Déconnexion réussie' });
});

// @route   GET /api/auth/verify
// @desc    Vérifier la validité du token
// @access  Private
router.get('/verify', authenticate, (req, res) => {
  res.json({
    valid: true,
    user: req.user
  });
});

// @route   POST /api/auth/unlock
// @desc    Déverrouiller un compte (pour les tests et admin)
// @access  Public (temporaire pour les tests)
router.post('/unlock', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        message: 'Email requis'
      });
    }

    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      return res.status(404).json({
        message: 'Utilisateur non trouvé'
      });
    }

    // Déverrouiller le compte
    await User.findByIdAndUpdate(user._id, {
      $unset: {
        loginAttempts: 1,
        lockUntil: 1
      }
    });

    console.log('🔓 Compte déverrouillé pour:', email);

    res.json({
      message: 'Compte déverrouillé avec succès',
      user: {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName
      }
    });

  } catch (error) {
    console.error('Erreur lors du déverrouillage:', error);
    res.status(500).json({
      message: 'Erreur serveur lors du déverrouillage'
    });
  }
});

module.exports = router;
