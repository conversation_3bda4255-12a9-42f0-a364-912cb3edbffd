const express = require('express');
const router = express.Router();
const Schedule = require('../models/Schedule');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, date, instructor, course } = req.query;
    const query = {};
    if (date) query.date = { $gte: new Date(date) };
    if (instructor) query.instructor = instructor;
    if (course) query.course = course;
    
    const schedules = await Schedule.find(query)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('module', 'moduleCode title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ date: 1, startTime: 1 });
    
    const total = await Schedule.countDocuments(query);
    res.json({ schedules, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const schedule = await Schedule.findById(req.params.id)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('participants.expectedStudents', 'studentNumber user');
    
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    res.json(schedule);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const schedule = new Schedule(req.body);
    await schedule.save();
    res.status(201).json(schedule);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const schedule = await Schedule.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    res.json(schedule);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/attendance', async (req, res) => {
  try {
    const { studentId, status, recordedBy, notes } = req.body;
    const schedule = await Schedule.findById(req.params.id);
    
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    
    await schedule.markAttendance(studentId, status, recordedBy, notes);
    res.json({ message: 'Attendance marked successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
