const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const Application = require('../models/Application');
const { sendStudentCredentials, sendRejectionEmail, sendInterviewEmail } = require('../services/emailService');

// Configuration multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/applications/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Seuls les fichiers PDF, DOC, DOCX, JPG, JPEG et PNG sont autorisés'));
    }
  }
});

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, intakeYear, program } = req.query;
    const query = {};
    if (status) query['applicationStatus.status'] = status;
    if (intakeYear) query['programInfo.intakeYear'] = intakeYear;
    if (program) query['programInfo.program'] = program;

    const applications = await Application.find(query)
      .populate('assignedTo', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Application.countDocuments(query);
    res.json({ applications, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const application = await Application.findById(req.params.id)
      .populate('assignedTo', 'firstName lastName email')
      .populate('interview.interviewers.interviewer', 'firstName lastName');

    if (!application) return res.status(404).json({ message: 'Application not found' });
    res.json(application);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route publique pour soumettre une candidature avec fichiers
router.post('/submit', upload.fields([
  { name: 'cv', maxCount: 1 },
  { name: 'motivationLetter', maxCount: 1 },
  { name: 'diploma', maxCount: 1 },
  { name: 'transcripts', maxCount: 1 },
  { name: 'photo', maxCount: 1 }
]), async (req, res) => {
  try {
    console.log('📝 Nouvelle candidature reçue');
    console.log('Données:', req.body);
    console.log('Fichiers:', req.files);

    // Préparer les données de candidature
    const applicationData = {
      personalInfo: {
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        phone: req.body.phone,
        dateOfBirth: req.body.dateOfBirth,
        gender: req.body.gender,
        nationality: req.body.nationality,
        address: {
          street: req.body.address || '',
          city: req.body.city || '',
          zipCode: req.body.postalCode || '',
          country: req.body.country || 'Tunisia'
        }
      },

      programInfo: {
        program: 'EMBA',
        specialization: req.body.specialization || 'General Management',
        intakeYear: new Date().getFullYear().toString(),
        intakeSemester: 'Fall',
        studyMode: 'part_time'
      },

      academicBackground: [{
        degree: req.body.lastDegree || 'Bachelor',
        fieldOfStudy: 'Business', // Valeur par défaut
        institution: req.body.institution,
        country: req.body.country || 'Tunisia',
        startYear: parseInt(req.body.graduationYear) - 4 || 2020,
        endYear: parseInt(req.body.graduationYear) || 2024,
        gpa: req.body.gpa ? parseFloat(req.body.gpa) : undefined,
        isHighestDegree: true
      }],

      workExperience: [{
        company: req.body.currentCompany,
        position: req.body.currentPosition,
        industry: req.body.industry,
        startDate: new Date(Date.now() - (parseInt(req.body.yearsOfExperience?.split('-')[0]) || 5) * 365 * 24 * 60 * 60 * 1000),
        isCurrent: true,
        managementLevel: 'Mid-Level',
        teamSize: parseInt(req.body.teamSize?.split('-')[0]) || 0
      }],

      leadership: {
        totalYearsExperience: parseInt(req.body.yearsOfExperience?.split('-')[0]) || 5,
        managementExperience: parseInt(req.body.managementExperience?.split('-')[0]) || 2
      },

      motivation: {
        whyMBA: req.body.whyEMBA || req.body.motivation,
        careerGoals: req.body.careerGoals,
        whyThisSchool: req.body.motivation,
        contribution: 'À définir lors de l\'entretien'
      },

      financing: {
        fundingSource: req.body.fundingSource || 'self_funded',
        totalCost: 25000, // Coût par défaut en TND
        currency: 'TND'
      },

      applicationStatus: {
        status: 'submitted',
        submissionDate: new Date()
      },

      documents: []
    };

    // Traiter les fichiers uploadés
    if (req.files) {
      Object.keys(req.files).forEach(fieldName => {
        const file = req.files[fieldName][0];
        let documentType = 'other';

        switch (fieldName) {
          case 'cv':
            documentType = 'cv_resume';
            break;
          case 'motivationLetter':
            documentType = 'personal_statement';
            break;
          case 'diploma':
            documentType = 'diploma';
            break;
          case 'transcripts':
            documentType = 'transcript';
            break;
          case 'photo':
            documentType = 'photo';
            break;
        }

        applicationData.documents.push({
          type: documentType,
          filename: file.filename,
          originalName: file.originalname,
          path: file.path,
          size: file.size,
          mimeType: file.mimetype,
          uploadDate: new Date()
        });
      });
    }

    // Créer la candidature
    const application = new Application(applicationData);
    await application.save();

    console.log('✅ Candidature créée avec succès:', application.applicationNumber);

    // Réponse de succès
    res.status(201).json({
      success: true,
      message: 'Candidature soumise avec succès !',
      applicationNumber: application.applicationNumber,
      applicationId: application._id
    });

  } catch (error) {
    console.error('❌ Erreur lors de la soumission:', error);
    res.status(400).json({
      success: false,
      message: 'Erreur lors de la soumission de la candidature',
      error: error.message
    });
  }
});

router.post('/', async (req, res) => {
  try {
    const application = new Application(req.body);
    await application.save();
    res.status(201).json(application);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const application = await Application.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!application) return res.status(404).json({ message: 'Application not found' });
    res.json(application);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/submit', async (req, res) => {
  try {
    const { submittedBy } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    if (!application.isComplete()) {
      return res.status(400).json({ message: 'Application is not complete' });
    }

    await application.submit(submittedBy);
    res.json({ message: 'Application submitted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Route pour changer le statut d'une candidature
router.patch('/:id/status', async (req, res) => {
  try {
    const { status, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    // Mettre à jour le statut
    application.applicationStatus.status = status;
    application.applicationStatus.statusHistory.push({
      status,
      changeDate: new Date(),
      reason,
      comments: reason
    });

    if (status === 'accepted') {
      application.applicationStatus.decisionDate = new Date();
      application.applicationStatus.decision = 'accepted';
    } else if (status === 'rejected') {
      application.applicationStatus.decisionDate = new Date();
      application.applicationStatus.decision = 'rejected';
      application.applicationStatus.decisionReason = reason;

      // Envoyer email de refus
      try {
        await sendRejectionEmail({
          firstName: application.personalInfo.firstName,
          lastName: application.personalInfo.lastName,
          email: application.personalInfo.email
        }, reason);
      } catch (emailError) {
        console.error('Erreur lors de l\'envoi de l\'email de refus:', emailError);
      }
    } else if (status === 'interview_scheduled') {
      // Envoyer email pour entretien
      try {
        await sendInterviewEmail({
          firstName: application.personalInfo.firstName,
          lastName: application.personalInfo.lastName,
          email: application.personalInfo.email
        });
      } catch (emailError) {
        console.error('Erreur lors de l\'envoi de l\'email d\'entretien:', emailError);
      }
    }

    await application.save();

    res.json({
      success: true,
      message: 'Statut mis à jour avec succès',
      application
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({ message: error.message });
  }
});

router.post('/:id/accept', async (req, res) => {
  try {
    const { acceptedBy, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.accept(acceptedBy, reason);
    res.json({ message: 'Application accepted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/reject', async (req, res) => {
  try {
    const { rejectedBy, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.reject(rejectedBy, reason);
    res.json({ message: 'Application rejected successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/schedule-interview', async (req, res) => {
  try {
    const { date, time, interviewers, scheduledBy } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.scheduleInterview(date, time, interviewers, scheduledBy);
    res.json({ message: 'Interview scheduled successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/statistics', async (req, res) => {
  try {
    const { intakeYear, program, status } = req.query;
    const filters = {};
    if (intakeYear) filters.intakeYear = intakeYear;
    if (program) filters.program = program;
    if (status) filters.status = status;

    const stats = await Application.getApplicationStatistics(filters);
    res.json(stats[0] || {});
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route pour créer un compte étudiant à partir d'une candidature acceptée
router.post('/:id/create-student', async (req, res) => {
  try {
    const User = require('../models/User');
    const Student = require('../models/Student');

    const application = await Application.findById(req.params.id);

    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    if (application.applicationStatus.status !== 'accepted') {
      return res.status(400).json({ message: 'La candidature doit être acceptée pour créer un compte étudiant' });
    }

    // Vérifier si un compte étudiant existe déjà
    const existingUser = await User.findOne({ email: application.personalInfo.email });
    if (existingUser) {
      return res.status(400).json({ message: 'Un compte existe déjà pour cet email' });
    }

    // Générer un mot de passe temporaire (utiliser la date de naissance ou un ID)
    const tempPassword = application.personalInfo.dateOfBirth ?
      application.personalInfo.dateOfBirth.toISOString().split('T')[0].replace(/-/g, '') :
      'EMBA2024';

    // Créer l'utilisateur
    const user = new User({
      firstName: application.personalInfo.firstName,
      lastName: application.personalInfo.lastName,
      email: application.personalInfo.email,
      phone: application.personalInfo.phone,
      password: tempPassword, // Sera hashé automatiquement par le middleware
      role: 'student',
      isActive: true,
      profile: {
        dateOfBirth: application.personalInfo.dateOfBirth,
        gender: application.personalInfo.gender,
        nationality: application.personalInfo.nationality,
        address: application.personalInfo.address
      }
    });

    await user.save();

    // Générer un numéro d'étudiant unique
    const currentYear = new Date().getFullYear();
    const studentCount = await Student.countDocuments();
    const studentNumber = `EMBA${currentYear}${String(studentCount + 1).padStart(3, '0')}`;

    // Créer le profil étudiant
    const student = new Student({
      user: user._id,
      studentNumber,
      academicYear: `${currentYear}-${currentYear + 1}`,
      cohort: `EMBA ${currentYear}`,
      program: application.programInfo?.program || 'EMBA',
      specialization: application.programInfo?.specialization,
      enrollmentStatus: 'enrolled',
      enrollmentDate: new Date(),
      expectedGraduationDate: new Date(currentYear + 2, 5, 30), // 2 ans plus tard
      tuitionFee: {
        total: application.financing?.totalCost || 25000,
        currency: application.financing?.currency || 'TND',
        paymentPlan: 'installments'
      }
    });

    await student.save();

    // Marquer la candidature comme traitée
    application.applicationStatus.studentAccountCreated = true;
    application.applicationStatus.createdStudentId = student._id;
    await application.save();

    // Envoyer un email avec les identifiants
    try {
      await sendStudentCredentials({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        studentNumber,
        temporaryPassword
      });
      console.log('✅ Email d\'identifiants envoyé à:', user.email);
    } catch (emailError) {
      console.error('❌ Erreur lors de l\'envoi de l\'email:', emailError);
    }

    res.json({
      success: true,
      message: 'Compte étudiant créé avec succès et email envoyé',
      studentNumber,
      email: user.email,
      temporaryPassword: tempPassword,
      studentId: student._id
    });

  } catch (error) {
    console.error('Erreur lors de la création du compte étudiant:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
