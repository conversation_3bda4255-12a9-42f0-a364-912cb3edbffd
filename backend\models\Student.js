const mongoose = require('mongoose');

const studentSchema = new mongoose.Schema({
  // Référence vers l'utilisateur
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  
  // Numéro d'étudiant unique
  studentNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // Informations académiques
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  cohort: {
    type: String,
    required: true,
    trim: true
  },
  program: {
    type: String,
    enum: ['EMBA', 'Executive MBA', 'Part-time MBA', 'Full-time MBA'],
    default: 'EMBA'
  },
  specialization: {
    type: String,
    enum: ['General Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Digital Transformation', 'Entrepreneurship'],
    trim: true
  },
  
  // Statut académique
  enrollmentStatus: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'graduated', 'dropped', 'deferred'],
    default: 'active'
  },
  enrollmentDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  expectedGraduationDate: {
    type: Date,
    required: true
  },
  actualGraduationDate: {
    type: Date
  },
  
  // Informations professionnelles (spécifiques EMBA)
  currentEmployment: {
    company: { type: String, trim: true },
    position: { type: String, trim: true },
    industry: { type: String, trim: true },
    yearsOfExperience: { type: Number, min: 0 },
    annualSalary: { type: Number, min: 0 },
    managementLevel: {
      type: String,
      enum: ['Entry Level', 'Mid-Level', 'Senior Level', 'Executive', 'C-Level']
    },
    teamSize: { type: Number, min: 0 },
    workLocation: {
      city: { type: String, trim: true },
      country: { type: String, trim: true }
    }
  },
  
  // Formation antérieure
  previousEducation: [{
    degree: { type: String, required: true, trim: true },
    institution: { type: String, required: true, trim: true },
    fieldOfStudy: { type: String, trim: true },
    graduationYear: { type: Number, min: 1950, max: new Date().getFullYear() },
    gpa: { type: Number, min: 0, max: 4 },
    country: { type: String, trim: true }
  }],
  
  // Certifications professionnelles
  certifications: [{
    name: { type: String, required: true, trim: true },
    issuingOrganization: { type: String, required: true, trim: true },
    issueDate: { type: Date },
    expirationDate: { type: Date },
    credentialId: { type: String, trim: true },
    isActive: { type: Boolean, default: true }
  }],
  
  // Informations financières
  tuitionFee: {
    total: { type: Number, required: true, min: 0 },
    paid: { type: Number, default: 0, min: 0 },
    remaining: { type: Number, default: 0, min: 0 },
    currency: { type: String, default: 'TND' },
    paymentPlan: {
      type: String,
      enum: ['full_payment', 'installments', 'employer_sponsored', 'scholarship'],
      default: 'installments'
    }
  },
  
  // Performance académique
  academicRecord: {
    currentGPA: { type: Number, min: 0, max: 4, default: 0 },
    cumulativeGPA: { type: Number, min: 0, max: 4, default: 0 },
    creditsCompleted: { type: Number, default: 0, min: 0 },
    creditsRequired: { type: Number, required: true, min: 0 },
    academicStanding: {
      type: String,
      enum: ['excellent', 'good', 'satisfactory', 'probation', 'warning'],
      default: 'satisfactory'
    }
  },
  
  // Présence et participation
  attendanceRecord: {
    totalSessions: { type: Number, default: 0 },
    attendedSessions: { type: Number, default: 0 },
    attendanceRate: { type: Number, default: 0, min: 0, max: 100 },
    absenceReasons: [{
      date: { type: Date },
      reason: { type: String, trim: true },
      isExcused: { type: Boolean, default: false }
    }]
  },
  
  // Projet final / Thèse
  finalProject: {
    title: { type: String, trim: true },
    description: { type: String, trim: true },
    supervisor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Professor'
    },
    status: {
      type: String,
      enum: ['not_started', 'proposal_submitted', 'in_progress', 'completed', 'defended'],
      default: 'not_started'
    },
    submissionDate: { type: Date },
    defenseDate: { type: Date },
    grade: { type: Number, min: 0, max: 20 }
  },
  
  // Informations de contact d'urgence
  emergencyContact: {
    name: { type: String, trim: true },
    relationship: { type: String, trim: true },
    phone: { type: String, trim: true },
    email: { type: String, trim: true, lowercase: true },
    address: { type: String, trim: true }
  },
  
  // Préférences d'apprentissage
  learningPreferences: {
    preferredSchedule: {
      type: String,
      enum: ['weekends', 'evenings', 'intensive_blocks', 'flexible']
    },
    learningStyle: {
      type: String,
      enum: ['visual', 'auditory', 'kinesthetic', 'reading_writing']
    },
    groupWorkPreference: {
      type: String,
      enum: ['individual', 'small_groups', 'large_groups', 'mixed']
    }
  },
  
  // Activités extracurriculaires
  extracurricularActivities: [{
    activity: { type: String, trim: true },
    role: { type: String, trim: true },
    startDate: { type: Date },
    endDate: { type: Date },
    description: { type: String, trim: true }
  }],
  
  // Réseautage et alumni
  networkingProfile: {
    linkedinUrl: { type: String, trim: true },
    industryInterests: [{ type: String, trim: true }],
    careerGoals: { type: String, trim: true },
    mentorshipInterest: {
      type: String,
      enum: ['mentor', 'mentee', 'both', 'none'],
      default: 'none'
    }
  },
  
  // Documents et fichiers
  documents: [{
    type: {
      type: String,
      enum: ['transcript', 'diploma', 'cv', 'recommendation_letter', 'id_copy', 'photo', 'other'],
      required: true
    },
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    path: { type: String, required: true },
    size: { type: Number },
    mimeType: { type: String },
    uploadDate: { type: Date, default: Date.now },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Notes et commentaires des professeurs/staff
  notes: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: { type: String, required: true, trim: true },
    category: {
      type: String,
      enum: ['academic', 'behavioral', 'administrative', 'personal', 'other'],
      default: 'academic'
    },
    isPrivate: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now }
  }],
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
studentSchema.index({ studentNumber: 1 });
studentSchema.index({ user: 1 });
studentSchema.index({ enrollmentStatus: 1 });
studentSchema.index({ academicYear: 1 });
studentSchema.index({ cohort: 1 });

// Virtual pour calculer l'âge
studentSchema.virtual('age').get(function() {
  if (!this.user || !this.user.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.user.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual pour calculer le taux de présence
studentSchema.virtual('attendancePercentage').get(function() {
  if (this.attendanceRecord.totalSessions === 0) return 0;
  return Math.round((this.attendanceRecord.attendedSessions / this.attendanceRecord.totalSessions) * 100);
});

// Virtual pour calculer le solde restant
studentSchema.virtual('remainingBalance').get(function() {
  return this.tuitionFee.total - this.tuitionFee.paid;
});

// Middleware pour générer automatiquement le numéro d'étudiant
studentSchema.pre('save', async function(next) {
  if (!this.studentNumber) {
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({
      studentNumber: new RegExp(`^EMBA${year}`)
    });
    this.studentNumber = `EMBA${year}${String(count + 1).padStart(4, '0')}`;
  }
  
  // Calculer le solde restant
  this.tuitionFee.remaining = this.tuitionFee.total - this.tuitionFee.paid;
  
  next();
});

// Méthode pour calculer la progression académique
studentSchema.methods.getAcademicProgress = function() {
  const progressPercentage = (this.academicRecord.creditsCompleted / this.academicRecord.creditsRequired) * 100;
  return Math.min(Math.round(progressPercentage), 100);
};

// Méthode pour vérifier l'éligibilité à la graduation
studentSchema.methods.isEligibleForGraduation = function() {
  return this.academicRecord.creditsCompleted >= this.academicRecord.creditsRequired &&
         this.academicRecord.currentGPA >= 2.0 &&
         this.tuitionFee.remaining <= 0 &&
         this.finalProject.status === 'defended';
};

// Méthode pour obtenir le profil complet de l'étudiant
studentSchema.methods.getFullProfile = function() {
  return this.populate('user', '-password -resetPasswordToken -verificationToken');
};

module.exports = mongoose.model('Student', studentSchema);
