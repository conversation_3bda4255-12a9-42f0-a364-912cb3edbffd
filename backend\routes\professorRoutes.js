const express = require('express');
const router = express.Router();
const Professor = require('../models/Professor');

// GET /api/professors - Obtenir tous les professeurs
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, department, employmentStatus } = req.query;
    const query = {};
    
    if (department) query.department = department;
    if (employmentStatus) query.employmentStatus = employmentStatus;
    
    const professors = await Professor.find(query)
      .populate('user', 'firstName lastName email phone profilePicture')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Professor.countDocuments(query);
    
    res.json({
      professors,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id - Obtenir un professeur par ID
router.get('/:id', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id)
      .populate('user', '-password -resetPasswordToken -verificationToken');
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/professors - Créer un nouveau professeur
router.post('/', async (req, res) => {
  try {
    const professor = new Professor(req.body);
    await professor.save();
    
    const populatedProfessor = await Professor.findById(professor._id)
      .populate('user', 'firstName lastName email');
    
    res.status(201).json(populatedProfessor);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// PUT /api/professors/:id - Mettre à jour un professeur
router.put('/:id', async (req, res) => {
  try {
    const professor = await Professor.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.body.updatedBy },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/professors/:id/courses - Obtenir les cours d'un professeur
router.get('/:id/courses', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    const courses = await professor.getCurrentCourses();
    res.json(courses);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id/teaching-load - Obtenir la charge d'enseignement
router.get('/:id/teaching-load', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    const teachingLoad = await professor.getTeachingLoad();
    res.json(teachingLoad);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id/profile - Obtenir le profil public
router.get('/:id/profile', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor.getPublicProfile());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
