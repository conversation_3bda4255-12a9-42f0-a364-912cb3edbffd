const express = require('express');
const router = express.Router();
const Grade = require('../models/Grade');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, student, course, gradeType } = req.query;
    const query = {};
    if (student) query.student = student;
    if (course) query.course = course;
    if (gradeType) query.gradeType = gradeType;
    
    const grades = await Grade.find(query)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('assignment', 'title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Grade.countDocuments(query);
    res.json({ grades, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const grade = await Grade.findById(req.params.id)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('assignment', 'title type')
      .populate('gradingMetadata.gradedBy', 'firstName lastName');
    
    if (!grade) return res.status(404).json({ message: 'Grade not found' });
    res.json(grade);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const grade = new Grade(req.body);
    await grade.save();
    res.status(201).json(grade);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const grade = await Grade.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!grade) return res.status(404).json({ message: 'Grade not found' });
    res.json(grade);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/publish', async (req, res) => {
  try {
    const { publishedBy } = req.body;
    const grade = await Grade.findById(req.params.id);
    
    if (!grade) return res.status(404).json({ message: 'Grade not found' });
    
    await grade.publish(publishedBy);
    res.json({ message: 'Grade published successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
