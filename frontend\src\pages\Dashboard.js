import React from 'react';
import {
  AcademicCapIcon,
  UserGroupIcon,
  BookOpenIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';
import StatsCard from '../components/Dashboard/StatsCard';

// Helper functions pour les classes CSS dynamiques
const getActivityIconClasses = (color) => {
  const classes = {
    primary: 'flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900',
    success: 'flex h-8 w-8 items-center justify-center rounded-full bg-success-100 dark:bg-success-900',
    info: 'flex h-8 w-8 items-center justify-center rounded-full bg-info-100 dark:bg-info-900',
    warning: 'flex h-8 w-8 items-center justify-center rounded-full bg-warning-100 dark:bg-warning-900'
  };
  return classes[color] || classes.primary;
};

const getActivityTextClasses = (color) => {
  const classes = {
    primary: 'h-4 w-4 text-primary-600 dark:text-primary-400',
    success: 'h-4 w-4 text-success-600 dark:text-success-400',
    info: 'h-4 w-4 text-info-600 dark:text-info-400',
    warning: 'h-4 w-4 text-warning-600 dark:text-warning-400'
  };
  return classes[color] || classes.primary;
};

const Dashboard = () => {
  // Données exemple (à remplacer par de vraies données de l'API)
  const stats = [
    {
      title: 'Total Étudiants',
      value: '2',
      change: '+2 ce mois',
      changeType: 'increase',
      icon: AcademicCapIcon,
      color: 'primary'
    },
    {
      title: 'Professeurs Actifs',
      value: '2',
      change: 'Stable',
      changeType: 'neutral',
      icon: UserGroupIcon,
      color: 'success'
    },
    {
      title: 'Cours Disponibles',
      value: '2',
      change: '+2 nouveaux',
      changeType: 'increase',
      icon: BookOpenIcon,
      color: 'info'
    },
    {
      title: 'Revenus ce Mois',
      value: '35,000 TND',
      change: '+15%',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
      color: 'warning'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'enrollment',
      message: 'Ahmed Ben Ali s\'est inscrit au cours Strategic Management',
      time: 'Il y a 2 heures',
      icon: AcademicCapIcon,
      color: 'primary'
    },
    {
      id: 2,
      type: 'payment',
      message: 'Paiement reçu de Fatma Trabelsi - 25,000 TND',
      time: 'Il y a 4 heures',
      icon: CurrencyDollarIcon,
      color: 'success'
    },
    {
      id: 3,
      type: 'course',
      message: 'Nouveau cours Corporate Finance ajouté par Dr. Leila Mansouri',
      time: 'Il y a 1 jour',
      icon: BookOpenIcon,
      color: 'info'
    },
    {
      id: 4,
      type: 'grade',
      message: 'Notes publiées pour le cours Strategic Management',
      time: 'Il y a 2 jours',
      icon: ChartBarIcon,
      color: 'warning'
    }
  ];

  const upcomingEvents = [
    {
      id: 1,
      title: 'Cours Strategic Management',
      time: '09:00 - 12:00',
      date: 'Aujourd\'hui',
      instructor: 'Dr. Mohamed Gharbi',
      students: 15
    },
    {
      id: 2,
      title: 'Examen Corporate Finance',
      time: '14:00 - 16:00',
      date: 'Demain',
      instructor: 'Dr. Leila Mansouri',
      students: 12
    },
    {
      id: 3,
      title: 'Réunion Professeurs',
      time: '10:00 - 11:00',
      date: 'Vendredi',
      instructor: 'Administration',
      students: null
    }
  ];

  return (
    <div className="w-full max-w-none space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:tracking-tight">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Vue d'ensemble de votre système EMBA
          </p>
        </div>
        <div className="flex-shrink-0">
          <button className="btn btn-primary w-full md:w-auto">
            <CalendarDaysIcon className="h-4 w-4 mr-2" />
            Voir Planning
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 xl:gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            icon={stat.icon}
            color={stat.color}
            className="animate-fade-in w-full"
            style={{ animationDelay: `${index * 100}ms` }}
          />
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">

        {/* Recent Activities */}
        <div className="card">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Activités Récentes
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Dernières actions dans le système
            </p>
          </div>
          <div className="p-6">
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivities.map((activity, index) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {index !== recentActivities.length - 1 && (
                        <span
                          className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700"
                          aria-hidden="true"
                        />
                      )}
                      <div className="relative flex space-x-3">
                        <div className={getActivityIconClasses(activity.color)}>
                          <activity.icon className={getActivityTextClasses(activity.color)} />
                        </div>
                        <div className="flex min-w-0 flex-1 justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white">
                              {activity.message}
                            </p>
                          </div>
                          <div className="whitespace-nowrap text-right text-sm text-gray-500 dark:text-gray-400">
                            {activity.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="card">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Événements à Venir
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Planning des prochains cours et événements
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {upcomingEvents.map((event) => (
                <div
                  key={event.id}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {event.title}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {event.instructor}
                    </p>
                    <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>{event.date}</span>
                      <span>{event.time}</span>
                      {event.students && (
                        <span>{event.students} étudiants</span>
                      )}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <CalendarDaysIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
